<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import java.lang.*?>
<?import javafx.scene.layout.*?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0" style="-fx-background-color: #D3D3D3;" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <HBox prefHeight="104.0" prefWidth="1326.0" style="-fx-border-color: #33adff;">
         <children>
            <VBox prefHeight="102.0" prefWidth="231.0">
               <children>
                  <Pane prefHeight="102.0" prefWidth="237.0">
                     <children>
                        <ImageView fx:id="aimsImage" fitHeight="110.0" fitWidth="100.0" layoutX="50.0" pickOnBounds="true" preserveRatio="true" style="-fx-cursor: hand;">
                           <image>
                              <Image url="@logos/aim_logo.png" />
                           </image>
                        </ImageView>
                     </children>
                  </Pane>
               </children>
            </VBox>
            <HBox prefHeight="102.0" prefWidth="600.0">
               <children>
                  <Pane prefHeight="102.0" prefWidth="562.0">
                     <children>
                        <TextField fx:id="searchTextField" layoutX="17.0" layoutY="27.0" prefHeight="48.0" prefWidth="407.0" />
                     </children>
                  </Pane>
                  <Pane prefHeight="102.0" prefWidth="397.0">
                     <children>
                        <SplitMenuButton fx:id="splitMenuBtnSearch" layoutX="50.0" layoutY="27.0" mnemonicParsing="false" prefHeight="48.0" prefWidth="105.0" text="Search">
                          <items>
                            <MenuItem mnemonicParsing="false" text="Action 1" />
                            <MenuItem mnemonicParsing="false" text="Action 2" />
                          </items>
                        </SplitMenuButton>
                        <ChoiceBox fx:id="choiceBoxOrder" layoutX="180.0" layoutY="27.0" prefHeight="48.0" prefWidth="120.0" />
                     </children>
                  </Pane>
               </children>
            </HBox>
            <HBox prefHeight="102.0" prefWidth="480.0">
               <children>
                  <Pane prefHeight="102.0" prefWidth="480.0">
                     <children>
                        <Button fx:id="cartButton" layoutX="180.0" layoutY="20.0" mnemonicParsing="false" prefHeight="60.0" prefWidth="120.0" text="CART" />
                        <Button fx:id="orderButton" layoutX="20.0" layoutY="20.0" mnemonicParsing="false" prefHeight="60.0" prefWidth="120.0" text="ORDER" />
                        <Button layoutX="330.0" layoutY="20.0" mnemonicParsing="false" prefHeight="60.0" prefWidth="120.0" text="ACCOUNT" />
                     </children></Pane>
               </children></HBox>
         </children>
      </HBox>
      <VBox layoutY="103.0" prefHeight="678.0" prefWidth="1326.0" style="-fx-background-color: #D3D3D3;">
         <children>
            <HBox fx:id="hboxMedia" layoutX="24.0" layoutY="123.0" prefHeight="600.0" prefWidth="1327.0" style="-fx-background-color: #D3D3D3;">
               <children>
                  <VBox fx:id="vboxMedia1" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia2" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia3" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia4" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia5" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
               </children>
            </HBox>
            <HBox alignment="CENTER" layoutX="24.0" layoutY="723.0" prefHeight="50.0" prefWidth="1327.0" spacing="10.0">
               <children>
                  <Button fx:id="btnPrevPage" alignment="CENTER" mnemonicParsing="false" prefHeight="30.0" prefWidth="76.0" text="Previous" />
                  <Label fx:id="lblPageInfo" alignment="CENTER" contentDisplay="CENTER" text="Page 1 of x" />
                  <Button fx:id="btnNextPage" mnemonicParsing="false" prefHeight="30.0" prefWidth="76.0" text="Next" />
               </children>
            </HBox>
         </children>
      </VBox>
   </children>
</AnchorPane>
